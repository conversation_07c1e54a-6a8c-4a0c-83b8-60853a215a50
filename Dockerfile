# Utilise Python 3.11 slim comme base
FROM python:3.11-slim

# Éviter les invites interactives
ENV DEBIAN_FRONTEND=noninteractive

# Installation des dépendances nécessaires à Playwright
RUN apt-get update && apt-get install -y --no-install-recommends \
    wget \
    gnupg \
    curl && \
    pip install playwright && \
    playwright install-deps && \
    playwright install && \
    rm -rf /var/lib/apt/lists/*  # Nettoyage du cache APT

# Copier requirements.txt et installer les dépendances Python
COPY requirements.txt /app/requirements.txt
RUN pip install --no-cache-dir -r /app/requirements.txt

# Copier le reste de l’application
COPY . /app

# Définir le répertoire de travail
WORKDIR /app

# Exposer le port 5000 (FastAPI écoute sur ce port)
EXPOSE 5000

# Commande par défaut: lancer l'API FastAPI via uvicorn
CMD ["python", "server.py"]
# Commande par défaut pour exécuter l'application