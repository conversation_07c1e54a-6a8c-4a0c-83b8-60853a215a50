import asyncio
from browser_use import Agent
from browser_use.llm import ChatOpenAI
from dotenv import load_dotenv

load_dotenv()

async def main():
    llm = ChatOpenAI(model="gpt-4o")
    
    agent = Agent(
        task="Compare the price of gpt-4o and DeepSeek-V3",
        llm=llm,
    )
    result = await agent.run()
    print("Agent execution completed!")
    print("Result:", result)

if __name__ == "__main__":
    asyncio.run(main())
