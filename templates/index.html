<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>JobScraper - Extraction d'offres d'emploi</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
    body {
      padding-top: 2rem;
      padding-bottom: 2rem;
    }
    .job-card {
      margin-bottom: 1rem;
      transition: all 0.3s;
    }
    .job-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    #loader {
      display: none;
    }
    .status-pending {
      color: #f39c12;
    }
    .status-running {
      color: #3498db;
    }
    .status-completed {
      color: #2ecc71;
    }
    .status-failed {
      color: #e74c3c;
    }
  </style>
</head>
<body>
<div class="container">
  <div class="row mb-4">
    <div class="col-12">
      <h1 class="text-center">JobScraper</h1>
      <p class="text-center lead">Outil d'extraction et de gestion des offres d'emploi</p>
    </div>
  </div>

  <!-- Formulaire de scraping -->
  <div class="row mb-4">
    <div class="col-md-8 offset-md-2">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">Extraction d'offres d'emploi</h5>
        </div>
        <div class="card-body">
          <form id="scrapeForm">
            <div class="mb-3">
              <label for="url" class="form-label">URL du site carrière</label>
              <input type="url" class="form-control" id="url" name="url" required placeholder="https://entreprise.jobs/fr/postuler/">
            </div>
            <div class="mb-3">
              <label for="maxJobs" class="form-label">Nombre maximum d'offres à extraire</label>
              <input type="number" class="form-control" id="maxJobs" name="maxJobs" min="1" max="20" value="5">
            </div>
            <div class="mb-3">
              <label for="companyName" class="form-label">Nom de l'entreprise (optionnel)</label>
              <input type="text" class="form-control" id="companyName" name="companyName">
            </div>
            <div class="mb-3">
              <label for="feedId" class="form-label">ID du flux (optionnel, généré automatiquement si vide)</label>
              <input type="text" class="form-control" id="feedId" name="feedId" placeholder="mon-entreprise-jobs">
            </div>
            <button type="submit" class="btn btn-primary w-100">Lancer l'extraction</button>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Statut et résultats -->
  <div class="row mb-4">
    <div class="col-md-8 offset-md-2">
      <div id="statusContainer" style="display: none;">
        <div class="card">
          <div class="card-header bg-info text-white">
            <h5 class="mb-0">Statut de l'extraction</h5>
          </div>
          <div class="card-body">
            <div class="d-flex align-items-center mb-3">
              <div class="spinner-border text-primary me-3" role="status" id="loader">
                <span class="visually-hidden">Chargement...</span>
              </div>
              <div>
                <p class="mb-1">Tâche: <span id="taskId"></span></p>
                <p class="mb-1">Statut: <span id="taskStatus"></span></p>
              </div>
            </div>
            <div id="resultContainer" style="display: none;">
              <hr>
              <h6>Résultats:</h6>
              <p>Nombre d'offres extraites: <span id="jobCount">0</span></p>
              <p>URL du flux JSON: <a href="#" id="feedUrl" target="_blank"></a></p>
              <button class="btn btn-sm btn-outline-secondary" id="copyUrlBtn">Copier l'URL</button>
            </div>
            <div id="errorContainer" style="display: none;">
              <hr>
              <div class="alert alert-danger" role="alert">
                <h6>Erreur:</h6>
                <p id="errorMessage"></p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Liste des flux disponibles -->
  <div class="row">
    <div class="col-md-8 offset-md-2">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">Flux disponibles</h5>
        </div>
        <div class="card-body">
          <button id="refreshFeeds" class="btn btn-sm btn-outline-secondary mb-3">Rafraîchir la liste</button>
          <div id="feedsList">
            <p class="text-center text-muted">Chargement des flux...</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // Fonction pour lancer une extraction
  document.getElementById('scrapeForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    // Récupérer les valeurs du formulaire
    const url = document.getElementById('url').value;
    const maxJobs = document.getElementById('maxJobs').value;
    const companyName = document.getElementById('companyName').value;
    const feedId = document.getElementById('feedId').value;

    // Préparer les données à envoyer
    const data = {
      url: url,
      max_jobs: parseInt(maxJobs),
      company_name: companyName || null,
      feed_id: feedId || null
    };

    try {
      // Envoyer la requête
      const response = await fetch('/api/scrape', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        throw new Error('Erreur lors du lancement de l\'extraction');
      }

      const result = await response.json();

      // Afficher le conteneur de statut
      document.getElementById('statusContainer').style.display = 'block';
      document.getElementById('loader').style.display = 'inline-block';
      document.getElementById('taskId').textContent = result.task_id;
      document.getElementById('taskStatus').textContent = result.status;
      document.getElementById('taskStatus').className = 'status-' + result.status;

      // Masquer les résultats et les erreurs
      document.getElementById('resultContainer').style.display = 'none';
      document.getElementById('errorContainer').style.display = 'none';

      // Lancer la vérification périodique du statut
      checkTaskStatus(result.task_id);

    } catch (error) {
      console.error('Erreur:', error);
      alert('Erreur: ' + error.message);
    }
  });

  // Fonction pour vérifier le statut d'une tâche
  async function checkTaskStatus(taskId) {
    try {
      const response = await fetch(`/api/tasks/${taskId}`);
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération du statut');
      }

      const task = await response.json();

      // Mettre à jour le statut
      document.getElementById('taskStatus').textContent = task.status;
      document.getElementById('taskStatus').className = 'status-' + task.status;

      // Si la tâche est terminée ou a échoué
      if (task.status === 'completed') {
        document.getElementById('loader').style.display = 'none';
        document.getElementById('resultContainer').style.display = 'block';
        document.getElementById('jobCount').textContent = task.result.job_count;

        const feedUrl = window.location.origin + task.result.feed_url;
        document.getElementById('feedUrl').textContent = feedUrl;
        document.getElementById('feedUrl').href = feedUrl;

        // Rafraîchir la liste des flux
        loadFeeds();

      } else if (task.status === 'failed') {
        document.getElementById('loader').style.display = 'none';
        document.getElementById('errorContainer').style.display = 'block';
        document.getElementById('errorMessage').textContent = task.error || 'Une erreur est survenue';

      } else {
        // Si la tâche est toujours en cours, vérifier à nouveau dans 2 secondes
        setTimeout(() => checkTaskStatus(taskId), 2000);
      }

    } catch (error) {
      console.error('Erreur:', error);
      document.getElementById('loader').style.display = 'none';
      document.getElementById('errorContainer').style.display = 'block';
      document.getElementById('errorMessage').textContent = error.message;
    }
  }

  // Fonction pour charger la liste des flux
  async function loadFeeds() {
    try {
      const response = await fetch('/api/feeds');
      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des flux');
      }

      const data = await response.json();
      const feedsList = document.getElementById('feedsList');

      if (data.feeds.length === 0) {
        feedsList.innerHTML = '<p class="text-center text-muted">Aucun flux disponible</p>';
        return;
      }

      let html = '<div class="list-group">';
      data.feeds.forEach(feed => {
        const feedUrl = window.location.origin + feed.url;
        html += `
                        <a href="${feedUrl}" target="_blank" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                            ${feed.feed_id}
                            <span class="badge bg-primary rounded-pill">Voir</span>
                        </a>
                    `;
      });
      html += '</div>';

      feedsList.innerHTML = html;

    } catch (error) {
      console.error('Erreur:', error);
      document.getElementById('feedsList').innerHTML = '<p class="text-center text-danger">Erreur lors du chargement des flux</p>';
    }
  }

  // Copier l'URL du flux dans le presse-papier
  document.getElementById('copyUrlBtn').addEventListener('click', function() {
    const feedUrl = document.getElementById('feedUrl').textContent;
    navigator.clipboard.writeText(feedUrl).then(() => {
      alert('URL copiée dans le presse-papier');
    });
  });

  // Rafraîchir la liste des flux
  document.getElementById('refreshFeeds').addEventListener('click', function() {
    loadFeeds();
  });

  // Charger la liste des flux au chargement de la page
  document.addEventListener('DOMContentLoaded', function() {
    loadFeeds();
  });
</script>
</body>
</html>